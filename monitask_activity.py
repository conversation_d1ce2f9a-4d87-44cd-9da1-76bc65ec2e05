#!/usr/bin/python3
import time
import subprocess
import sys
import os
import argparse
import signal
import random
import re
from datetime import datetime, timedelta

class MonitaskActivitySimulator:
    """
    Simulate minimal activity while screen is locked to ensure
    Monitask records time, without disrupting the lock state.
    """

    def __init__(self, duration=None, interval=60, quiet=False, stealth=True,
                 tab_switching=False, tab_interval=300):
        self.start_time = datetime.now()
        self.duration = duration  # in minutes
        self.interval = interval  # seconds between activity
        self.quiet = quiet
        self.stealth = stealth    # minimal, non-disruptive activity
        self.tab_switching = tab_switching  # enable tab switching
        self.tab_interval = tab_interval  # seconds between tab switches (default: 5 minutes)
        self.running = False
        self.action_count = 0
        self.last_tab_switch = datetime.now()
        self.tab_switch_count = 0

        # Set up clean shutdown on signals
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)

    def handle_signal(self, signum, frame):
        """Handle termination signals gracefully"""
        self.log("\nShutting down...")
        self.running = False

    def log(self, message):
        """Print message if not in quiet mode"""
        if not self.quiet:
            print(message)

    def format_remaining_time(self):
        """Format remaining time as a string"""
        if not self.duration:
            return "indefinitely"

        elapsed = datetime.now() - self.start_time
        remaining_seconds = (self.duration * 60) - elapsed.total_seconds()

        if remaining_seconds <= 0:
            return "ending now"

        remaining = timedelta(seconds=int(remaining_seconds))
        hours, remainder = divmod(remaining.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        if hours > 0:
            return f"{hours}h {minutes}m remaining"
        else:
            return f"{minutes}m {seconds}s remaining"

    def prevent_sleep(self):
        """Prevent system from sleeping but ALLOW screen to lock"""
        try:
            # Prevent system sleep using systemd-inhibit
            subprocess.run([
                "systemd-inhibit", "--what=sleep", "--who=monitask-helper",
                "--why=Keeping Monitask time tracking running", "--mode=block",
                "sleep", "1"
            ], stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)

            return True
        except Exception as e:
            self.log(f"Error preventing sleep: {e}")
            return False

    def simulate_activity(self):
        """
        Simulate minimal activity that Monitask will detect
        without disrupting the screen lock
        """
        try:
            # Different activity simulation methods ranked by stealth level
            if self.stealth:
                # Method 1: Use xdotool to make small mouse movements that
                # don't unlock the screen but are detectable by activity monitors
                try:
                    # Get current mouse position
                    mouse_pos = subprocess.run(
                        ["xdotool", "getmouselocation"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.DEVNULL,
                        text=True
                    )

                    if mouse_pos.returncode == 0:
                        # Extract current position
                        pos_info = mouse_pos.stdout.strip().split()
                        cur_x = int(pos_info[0].split(':')[1])
                        cur_y = int(pos_info[1].split(':')[1])

                        # Move mouse by tiny amount (1-2 pixels) and back
                        # This is typically too small to unlock screen but registers as activity
                        x_delta = random.randint(1, 2)
                        y_delta = random.randint(1, 2)

                        # Move mouse slightly
                        subprocess.run(
                            ["xdotool", "mousemove_relative", "--", str(x_delta), str(y_delta)],
                            stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                        )
                        time.sleep(0.5)

                        # Move back to avoid drift
                        subprocess.run(
                            ["xdotool", "mousemove", str(cur_x), str(cur_y)],
                            stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                        )

                        return "Simulated minimal mouse movement"
                except Exception:
                    # Fall back to next method if xdotool fails
                    pass

                # Method the most stealth: Send null keystroke that doesn't unlock screen
                # This avoids any visual changes while still registering as activity
                try:
                    # The Shift key alone won't unlock most screens but registers as activity
                    subprocess.run(
                        ["xdotool", "key", "shift"],
                        stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                    )
                    return "Simulated silent key press"
                except Exception:
                    pass

                # Method 3: Use DBUS to simulate user activity at system level
                try:
                    subprocess.run([
                        "dbus-send", "--session", "--dest=org.gnome.ScreenSaver",
                        "--type=method_call", "/org/gnome/ScreenSaver",
                        "org.gnome.ScreenSaver.SimulateUserActivity"
                    ], stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL)
                    return "Simulated system activity via DBUS"
                except Exception:
                    pass

            # Last resort: Touch a file to create activity
            activity_file = os.path.expanduser("~/.monitask_activity")
            with open(activity_file, "w") as f:
                f.write(f"Activity: {datetime.now().isoformat()}")
            return "Updated activity file"

        except Exception as e:
            self.log(f"Error simulating activity: {e}")
            return "Failed to simulate activity"

    def is_monitask_running(self):
        """Check if Monitask process is running"""
        try:
            # Method 1: Direct process check
            result = subprocess.run(
                ["pgrep", "-fl", "monitask"],
                stdout=subprocess.PIPE,
                stderr=subprocess.DEVNULL,
                text=True
            )
            if result.returncode == 0:
                return True

            # Method 2: Look in specific path where we know it's installed
            monitask_path = "/home/<USER>/.local/bin/monitaskd/Monitask.Stealth.Linux"
            if os.path.exists(monitask_path):
                result = subprocess.run(
                    ["pgrep", "-f", os.path.basename(monitask_path)],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.DEVNULL,
                    text=True
                )
                if result.returncode == 0:
                    return True

            return False
        except Exception:
            return False

    def get_active_browser(self):
        """Detect which browser is currently active"""
        try:
            # Get the active window information
            result = subprocess.run(
                ["xdotool", "getactivewindow", "getwindowname"],
                stdout=subprocess.PIPE,
                stderr=subprocess.DEVNULL,
                text=True
            )

            if result.returncode == 0:
                window_name = result.stdout.strip().lower()

                # Check for common browsers in window title
                browsers = {
                    'chrome': ['chrome', 'chromium'],
                    'firefox': ['firefox', 'mozilla'],
                    'edge': ['edge', 'microsoft edge'],
                    'safari': ['safari'],
                    'opera': ['opera'],
                    'brave': ['brave']
                }

                for browser, keywords in browsers.items():
                    if any(keyword in window_name for keyword in keywords):
                        return browser

            # Fallback: Check running processes for browsers
            browser_processes = {
                'chrome': ['chrome', 'chromium', 'google-chrome'],
                'firefox': ['firefox'],
                'edge': ['msedge', 'microsoft-edge'],
                'opera': ['opera'],
                'brave': ['brave']
            }

            for browser, processes in browser_processes.items():
                for process in processes:
                    result = subprocess.run(
                        ["pgrep", "-f", process],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.DEVNULL
                    )
                    if result.returncode == 0:
                        return browser

            return None

        except Exception as e:
            self.log(f"Error detecting browser: {e}")
            return None

    def switch_browser_tab(self):
        """Switch to the next tab in the active browser"""
        try:
            browser = self.get_active_browser()

            if not browser:
                # If no browser detected, try generic tab switching
                subprocess.run(
                    ["xdotool", "key", "ctrl+Tab"],
                    stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                )
                return "Switched tab (generic)"

            # Browser-specific tab switching
            if browser in ['chrome', 'edge', 'opera', 'brave']:
                # Chrome-based browsers: Ctrl+Tab or Ctrl+PageDown
                subprocess.run(
                    ["xdotool", "key", "ctrl+Tab"],
                    stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                )
                return f"Switched tab in {browser.title()}"

            elif browser == 'firefox':
                # Firefox: Ctrl+Tab or Ctrl+PageDown
                subprocess.run(
                    ["xdotool", "key", "ctrl+Tab"],
                    stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                )
                return f"Switched tab in Firefox"

            else:
                # Generic fallback
                subprocess.run(
                    ["xdotool", "key", "ctrl+Tab"],
                    stderr=subprocess.DEVNULL, stdout=subprocess.DEVNULL
                )
                return f"Switched tab in {browser}"

        except Exception as e:
            self.log(f"Error switching tab: {e}")
            return "Failed to switch tab"

    def should_switch_tab(self):
        """Check if it's time to switch tabs"""
        if not self.tab_switching:
            return False

        elapsed = (datetime.now() - self.last_tab_switch).total_seconds()
        return elapsed >= self.tab_interval

    def run(self):
        """Run the activity simulation"""
        end_time = None
        if self.duration:
            end_time = self.start_time + timedelta(minutes=self.duration)
            self.log(f"Simulating activity for {self.duration} minutes (until {end_time.strftime('%H:%M:%S')})")
        else:
            self.log("Simulating activity indefinitely (until you press Ctrl+C)")

        self.log(f"Simulating activity every {self.interval} seconds")
        if self.tab_switching:
            self.log(f"Tab switching enabled - switching tabs every {self.tab_interval//60} minutes")
        self.log("SCREEN CAN LOCK - activity will still be recorded by Monitask")
        self.log("Press Ctrl+C to stop")

        # Check for Monitask
        if self.is_monitask_running():
            self.log("✓ Monitask is running - activity will be recorded")
        else:
            self.log("⚠️ WARNING: Monitask not detected! Start Monitask first.")

        # Check for browser if tab switching is enabled
        if self.tab_switching:
            browser = self.get_active_browser()
            if browser:
                self.log(f"✓ Detected browser: {browser.title()}")
            else:
                self.log("⚠️ No browser detected - tab switching will use generic method")

        self.running = True
        try:
            while self.running:
                self.action_count += 1

                # Check if duration has expired
                if end_time and datetime.now() >= end_time:
                    self.log("\nDuration expired, shutting down")
                    break

                # Check if it's time to switch tabs
                tab_switch_result = None
                if self.should_switch_tab():
                    tab_switch_result = self.switch_browser_tab()
                    self.last_tab_switch = datetime.now()
                    self.tab_switch_count += 1

                # Simulate activity and prevent sleep
                current_time = datetime.now().strftime("%H:%M:%S")
                remaining = self.format_remaining_time()

                # Perform activity simulation
                activity_result = self.simulate_activity()
                self.prevent_sleep()

                # Show status (less frequent as time goes on)
                if not self.quiet:
                    if self.action_count <= 5 or self.action_count % 10 == 0:
                        monitask_status = "✓" if self.is_monitask_running() else "✗"
                        status_msg = f"[{current_time}] Activity {self.action_count}: {activity_result} ({remaining}) - Monitask: {monitask_status}"

                        # Add tab switching info if it occurred
                        if tab_switch_result:
                            status_msg += f" | Tab: {tab_switch_result}"
                        elif self.tab_switching:
                            next_tab_switch = self.tab_interval - (datetime.now() - self.last_tab_switch).total_seconds()
                            if next_tab_switch > 0:
                                status_msg += f" | Next tab switch: {int(next_tab_switch//60)}m {int(next_tab_switch%60)}s"

                        self.log(status_msg)

                # Wait for the next interval with a small random variation
                # to make activity patterns look more natural
                variation = random.randint(-5, 5) if self.interval > 10 else 0
                sleep_time = max(5, self.interval + variation)
                time.sleep(sleep_time)

        except Exception as e:
            self.log(f"Error: {e}")

        # Final summary
        if self.tab_switching and self.tab_switch_count > 0:
            self.log(f"Activity simulation ended - {self.tab_switch_count} tab switches performed")
        else:
            self.log("Activity simulation ended")


def check_dependencies():
    """Check if required tools are installed"""
    dependencies = [
        ("xdotool", "xdotool"),
        ("dbus-send", "dbus-x11"),
        ("systemd-inhibit", "systemd")
    ]

    missing = []
    for cmd, pkg in dependencies:
        try:
            subprocess.check_output(["which", cmd], stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            missing.append((cmd, pkg))

    if missing:
        print("Some tools are missing. For best results, install:")
        print(f"sudo apt-get install {' '.join(pkg for _, pkg in missing)}")
        # Only fail if xdotool is missing, as it's critical
        if "xdotool" in [pkg for _, pkg in missing]:
            print("ERROR: xdotool is required for activity simulation")
            return False

    return True


def main():
    parser = argparse.ArgumentParser(
        description="Simulate activity for Monitask while allowing screen lock",
        epilog="Example: python monitask_activity.py -t 480 --tab-switching (simulate for 8 hours with tab switching)"
    )
    parser.add_argument("-t", "--time", type=int, default=None,
                        help="Duration in minutes (default: run indefinitely)")
    parser.add_argument("-i", "--interval", type=int, default=60,
                        help="Seconds between activity simulations (default: 60)")
    parser.add_argument("-q", "--quiet", action="store_true",
                        help="Run in quiet mode with minimal output")
    parser.add_argument("-s", "--stealth", action="store_true", default=True,
                        help="Use stealth mode for minimal activity that doesn't disrupt lock")
    parser.add_argument("--tab-switching", action="store_true",
                        help="Enable automatic tab switching in browsers")
    parser.add_argument("--tab-interval", type=int, default=300,
                        help="Seconds between tab switches (default: 300 = 5 minutes)")

    args = parser.parse_args()

    if not check_dependencies():
        sys.exit(1)

    simulator = MonitaskActivitySimulator(
        duration=args.time,
        interval=args.interval,
        quiet=args.quiet,
        stealth=args.stealth,
        tab_switching=args.tab_switching,
        tab_interval=args.tab_interval
    )
    simulator.run()


if __name__ == "__main__":
    main()