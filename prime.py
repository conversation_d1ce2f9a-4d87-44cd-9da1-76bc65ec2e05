def find_kth_prime(k):
    """Find the kth prime number."""
    # For smaller k, we can use the direct checking method
    if k <= 1000:
        return find_kth_prime_direct(k)
    else:
        # For larger k, Sieve of Eratosthenes is more efficient
        return find_kth_prime_sieve(k)


def find_kth_prime_direct(k):
    """Find the kth prime number using direct primality testing."""
    if k == 1:
        return 2
    
    count = 1  # Start with 2 as the first prime
    num = 3    # Start checking from 3
    prime_checks = 0
    
    while count < k:
        prime_checks += 1
        if is_prime(num):
            count += 1
        
        if count == k:
            return num, prime_checks
            
        num += 2  # Check only odd numbers
    
    return num, prime_checks


def find_kth_prime_sieve(k):
    """Find the kth prime number using Sieve of Eratosthenes."""
    # Estimate an upper bound for the kth prime number
    if k <= 0:
        return 0, 0
    
    # Upper bound estimation based on Prime Number Theorem
    if k < 6:
        limit = 13  # Handle small values of k
    else:
        limit = int(k * (math.log(k) + math.log(math.log(k)))) + 1
    
    # Create the sieve
    sieve = [True] * (limit + 1)
    sieve[0] = sieve[1] = False
    sieve_operations = 0
    
    for i in range(2, int(limit**0.5) + 1):
        if sieve[i]:
            for j in range(i*i, limit + 1, i):
                sieve[j] = False
                sieve_operations += 1
    
    # Count primes until we reach the kth one
    count = 0
    for i in range(limit + 1):
        if sieve[i]:
            count += 1
            if count == k:
                return i, sieve_operations
    
    return -1, sieve_operations  # Should not reach here if upper bound is estimated correctly


def is_prime(n):
    """Check if a number is prime."""
    # Handle edge cases
    if n <= 1:
        return False
    if n <= 3:
        return True
    if n % 2 == 0 or n % 3 == 0:
        return False
    
    # Check divisibility by numbers of form 6k±1
    i = 5
    while i * i <= n:
        if n % i == 0 or n % (i + 2) == 0:
            return False
        i += 6
    
    return True


def benchmark_prime_algorithms(k_values):
    """Benchmark different algorithms for finding kth prime."""
    import time
    
    results = []
    
    for k in k_values:
        # Original method (naive approach)
        start_time = time.time()
        original_result = original_find_kth_prime(k)
        original_time = time.time() - start_time
        
        # Optimized direct method
        start_time = time.time()
        direct_result, direct_checks = find_kth_prime_direct(k)
        direct_time = time.time() - start_time
        
        # Sieve method
        start_time = time.time()
        sieve_result, sieve_ops = find_kth_prime_sieve(k)
        sieve_time = time.time() - start_time
        
        results.append({
            'k': k,
            'original': {
                'result': original_result,
                'time': original_time
            },
            'direct': {
                'result': direct_result,
                'time': direct_time,
                'checks': direct_checks
            },
            'sieve': {
                'result': sieve_result,
                'time': sieve_time,
                'operations': sieve_ops
            }
        })
    
    return results


def original_find_kth_prime(k):
    """Original implementation for comparison."""
    if k == 1:
        return 2
    count = 0
    num = 2

    while count < k:
        prime_check = True
        for i in range(2, int(num**0.5)+1):
            if num % i == 0:
                prime_check = False
                break
        
        if prime_check:
            count += 1
        
        num += 1

    return num - 1


def print_benchmark_results(results):
    """Print benchmark results in a readable format."""
    print("\n" + "="*80)
    print(f"{'k':^10}|{'Prime':^10}|{'Original Time':^15}|{'Direct Time':^15}|{'Sieve Time':^15}")
    print("-"*80)
    
    for r in results:
        print(f"{r['k']:^10}|{r['original']['result']:^10}|{r['original']['time']:^15.6f}|{r['direct']['time']:^15.6f}|{r['sieve']['time']:^15.6f}")
    
    print("\n" + "="*80)
    print("Algorithmic Complexity Analysis:")
    print("-"*80)
    print("Original Method: O(k * √n) time, O(1) space")
    print("Optimized Direct: O(k * √n) time with better constants, O(1) space")
    print("Sieve Method: O(n log log n) time, O(n) space where n ≈ k log k")
    print("="*80)


if __name__ == "__main__":
    import math
    import time
    
    # Simple example
    k = 5
    print(f"The {k}th prime number is {find_kth_prime(k)[0]}")
    
    # Run benchmarks with different k values
    print("\nRunning benchmarks...")
    k_values = [10, 100, 1000, 10000]
    results = benchmark_prime_algorithms(k_values)
    print_benchmark_results(results)
    
    # Find a specific k value with timing information
    k_input = int(input("\nEnter a value for k to find the kth prime: "))
    
    start = time.time()
    result, ops = find_kth_prime(k_input)
    end = time.time()
    
    print(f"The {k_input}th prime number is {result}")
    print(f"Time taken: {end - start:.6f} seconds")
    
    if k_input <= 1000:
        print(f"Algorithm used: Optimized Direct (with {ops} primality checks)")
    else:
        print(f"Algorithm used: Sieve of Eratosthenes (with {ops} sieve operations)")
    print(f"Space complexity: {'O(1)' if k_input <= 1000 else f'O(n) with n ≈ {int(k_input * (math.log(k_input) + math.log(math.log(k_input))) + 1)}'}")