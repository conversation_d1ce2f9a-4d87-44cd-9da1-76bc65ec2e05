---

## 🔢 PRIME NUMBER CHEAT SHEET

---

### ✅ 1. **Check if a Number is Prime**

```python
def is_prime(n):
    if n < 2: return False
    for i in range(2, int(n**0.5)+1):
        if n % i == 0: return False
    return True
```
🕒 Time: `O(√n)`

---

### ✅ 2. **Print All Primes ≤ n (Sieve of Eratosthenes)**

```python
def sieve(n):
    is_prime = [True] * (n+1)
    is_prime[0:2] = [False, False]
    for i in range(2, int(n**0.5)+1):
        if is_prime[i]:
            for j in range(i*i, n+1, i):
                is_prime[j] = False
    return [i for i, prime in enumerate(is_prime) if prime]
```
🕒 Time: `O(n log log n)`

---

### ✅ 3. **k-th Prime Number**

```python
def kth_prime(k, limit=100000):
    primes = sieve(limit)
    return primes[k-1]
```
🕒 Time: Depends on guess `limit`

---

### ✅ 4. **Prime Factorization (List)**

```python
def prime_factors(n):
    factors = []
    while n % 2 == 0:
        factors.append(2)
        n //= 2
    i = 3
    while i*i <= n:
        while n % i == 0:
            factors.append(i)
            n //= i
        i += 2
    if n > 1:
        factors.append(n)
    return factors
```
🕒 Time: `O(√n)`

---

### ✅ 5. **Count Primes < n**

```python
def count_primes(n):
    if n < 2: return 0
    is_prime = [True] * n
    is_prime[0:2] = [False, False]
    for i in range(2, int(n**0.5)+1):
        if is_prime[i]:
            for j in range(i*i, n, i):
                is_prime[j] = False
    return sum(is_prime)
```
🕒 Time: `O(n log log n)`

---

### ✅ 6. **Next Prime > n**

```python
def next_prime(n):
    while True:
        n += 1
        if is_prime(n):
            return n
```
🕒 Time: ~Depends on gap after `n`

---

### ✅ 7. **Largest Prime Factor**

```python
def largest_prime_factor(n):
    i = 2
    last = 0
    while i * i <= n:
        if n % i == 0:
            last = i
            n //= i
        else:
            i += 1
    return max(last, n)
```
🕒 Time: `O(√n)`

---

### ✅ 8. **Is Product of Two Primes**

```python
def is_product_of_two_primes(n):
    for i in range(2, int(n**0.5)+1):
        if n % i == 0 and is_prime(i) and is_prime(n // i):
            return True
    return False
```
🕒 Time: `O(√n × √n)`

---

### ✅ 9. **Sum of Primes ≤ n**

```python
def sum_of_primes(n):
    is_prime = [True] * (n+1)
    is_prime[0:2] = [False, False]
    for i in range(2, int(n**0.5)+1):
        if is_prime[i]:
            for j in range(i*i, n+1, i):
                is_prime[j] = False
    return sum(i for i, val in enumerate(is_prime) if val)
```
🕒 Time: `O(n log log n)`

---

### ✅ 10. **Goldbach's Conjecture**

```python
def goldbach(n):
    for i in range(2, n//2+1):
        if is_prime(i) and is_prime(n - i):
            return [i, n - i]
    return []
```
🕒 Time: `O(n × √n)`

---
---


### **1. Check if a number is prime**
**Problem**: Is `n` prime?  
**Algo**:  
- Return `False` if `n < 2`  
- Loop `i` from `2` to `sqrt(n)`  
  - If `n % i == 0` → not prime  
- Else → prime  
**Time**: O(√n)

---

### **2. Print all primes ≤ n**
**Problem**: List all primes up to `n`  
**Algo (Sieve of Eratosthenes)**:  
- Create a boolean array `is_prime[0...n]`  
- Mark all as `True`, then mark multiples of each prime as `False`  
- Collect all indices that are still `True`  
**Time**: O(n log log n)

---

### **3. Find the k-th prime number**
**Problem**: What is the k-th prime?  
**Algo**:  
- Use sieve to generate a **big list** of primes  
- Return the k-th one (0-indexed)  
**Time**: Depends on limit guessed; typically ~O(n log log n)

---

### **4. Prime Factorization**
**Problem**: List prime factors of `n`  
**Algo**:  
- Divide by 2 while divisible  
- Then try dividing by odd numbers up to `√n`  
- Remaining `n > 1` is also a prime  
**Time**: O(√n)

---

### **5. Count number of primes < n**
**Problem**: How many primes less than `n`?  
**Algo**:  
- Sieve of Eratosthenes  
- Return `sum(is_prime)`  
**Time**: O(n log log n)

---

### **6. Next Prime > n**
**Problem**: Smallest prime greater than `n`  
**Algo**:  
- Start from `n + 1`  
- Keep checking each number until one is prime  
**Time**: Worst case unbounded, but practical with trial division

---

### **7. Largest Prime Factor**
**Problem**: Find the largest prime that divides `n`  
**Algo**:  
- Try dividing `n` starting from 2  
- Last factor left when `n = 1` is the largest  
**Time**: O(√n)

---

### **8. Is Product of Two Primes**
**Problem**: Is `n = p * q` where p, q are primes?  
**Algo**:  
- Loop `i` from `2` to `√n`  
- If `n % i == 0`, check if `i` and `n//i` are both prime  
**Time**: O(√n × √n)

---

### **9. Sum of primes ≤ n**
**Problem**: Add all primes ≤ `n`  
**Algo**:  
- Use sieve to find primes  
- Sum up those indices that are still `True`  
**Time**: O(n log log n)

---

### **10. Goldbach’s Conjecture**
**Problem**: Find two primes that sum to even `n > 2`  
**Algo**:  
- Loop `i` from `2` to `n//2`  
- If both `i` and `n - i` are prime → return the pair  
**Time**: O(n × √n), or faster with sieve

---
---
