import math

# Step 1: Precompute primes up to 10^6
def generate_primes(limit):
    is_prime = [True] * (limit + 1)
    is_prime[0:2] = [False, False]
    
    for i in range(2, int(limit ** 0.5) + 1):
        if is_prime[i]:
            for j in range(i * i, limit + 1, i):
                is_prime[j] = False
    
    return [i for i, val in enumerate(is_prime) if val]

PRIME_CACHE = generate_primes(10**6)
MAX_CACHE = PRIME_CACHE[-1]

# Step 2: Trial division check for big numbers
def is_prime(n):
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        if n % i == 0:
            return False
    return True

# Step 3: Hybrid next_prime function
def next_prime(n):
    # If within cache range
    if n < MAX_CACHE:
        for p in PRIME_CACHE:
            if p > n:
                return p
    
    # Fallback for larger n
    candidate = n + 1
    while True:
        if is_prime(candidate):
            return candidate
        candidate += 1

print(next_prime(14))         # ➜ 17
print(next_prime(999_983))    # ➜ 1_000_003 (still in cache)
print(next_prime(10**6 + 1))  # ➜ uses trial division
