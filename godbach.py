def is_prime(x):
    if x<2:
        return False
        
    for i in range(2,int(x**0.5)+1):
        if x%i == 0:
            return False
            
    return True
    
    
    
def find_goldbach_pair(n):
    if n < 2 or n%2!=0:
        return []
        
    
    for i in range(2,n//2+1):
        if is_prime(i) and is_prime(n-i):
            return [i,n-i]
            
    return []
    

print(find_goldbach_pair(4))