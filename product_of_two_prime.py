def is_prime(x):
    if x < 2:
        return False
    for i in range(2, int(x**0.5) + 1):
        if x % i == 0:
            return False
    return True

def is_product_of_two_primes(n):
    for i in range(2, int(n**0.5) + 1):
        if n % i == 0:
            other = n // i
            if is_prime(i) and is_prime(other):
                return True
    return False

# Test
print(is_product_of_two_primes(15))  # ✅ True (3 x 5)
print(is_product_of_two_primes(8))   # ❌ False (2 x 2 x 2)
print(is_product_of_two_primes(9))   # ✅ True (3 x 3)
print(is_product_of_two_primes(7))   # ❌ False (only 1 prime)
