def prime_factors(n):
    
    factors = []
    
    if n%2==0:
        factors.append(2)
        n//=2
    
    i=3
    while i*i <= n:
        while n%i==0:
            factors.append(i)
            n//=i
            
        i+=2
    
    if n>1:
        factors.append(n)
        
    return factors
    
    


def prime_factors_count(n):
    
    fc = {}
    
    if n%2==0:
        fc[2]=fc.get(2,0) +1 
        n//=2
    
    i=3
    while i*i <= n:
        while n%i==0:
            fc[i]=fc.get(i,0)+1
            n//=i
            
        i+=2
    
    if n>1:
        fc[n] = fc.get(n,0)+1
        
    return fc
    
    


print(prime_factors_count(45))
print(prime_factors(45))